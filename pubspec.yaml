name: app
description: "<PERSON><PERSON><PERSON><PERSON>."

publish_to: "none"

version: 1.0.0+4

environment:
  sdk: ^3.5.3

dependencies:
  flutter:
    sdk: flutter


  ### Ui
  cached_network_image: ^3.3.1
  loading_indicator: ^3.1.1
  cupertino_icons: ^1.0.8
  flutter_svg: ^2.0.9
  carousel_slider: ^5.0.0
  loading_animation_widget: ^1.2.1
  toastification: ^2.3.0
  google_fonts: ^6.2.1

  ### Dialogs & Animations
  flutter_animate: ^4.4.1

  ### State Management
  get: 4.6.6

  ### Network
  dio: ^5.4.0

  ### Database
  get_storage: ^2.1.1

  ### Utils
  url_launcher: ^6.2.4
  path_provider: ^2.1.2
  flutter_screenutil: ^5.9.0
  permission_handler: ^11.2.0
  share_plus: ^10.0.3
  flutter_launcher_icons: ^0.14.1
  platform: ^3.1.4

  ### Maps
  mapbox_maps_flutter: ^2.3.0

  ### Firebase
  flutter_local_notifications: ^17.2.3
  firebase_core: ^3.6.0
  firebase_messaging: ^15.1.3
  firebase_database: ^11.1.4

  ### Background Services
  flutter_background_service: ^5.0.10

  zextensions:
    git:
      url: https://github.com/hurab/extensions_package
      ref: main # branch name
  webview_flutter: ^4.10.0
  dropdown_search: ^6.0.1
  lottie: ^3.1.3
  image_picker: ^1.1.2
  file_picker: ^8.1.3
  device_info_plus: ^11.3.0
  flutter_localizations:
    sdk: flutter
  flutter_map: ^7.0.2
  latlong2: ^0.9.1
  http: ^1.4.0
  routing_client_dart: ^1.0.6
  geolocator: ^14.0.0
  pretty_dio_logger: ^1.4.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0
  envied_generator: ^0.5.4+1

dependency_overrides:
  geolocator_android: 4.6.1

flutter_assets:
  assets_path: assets/
  output_path: lib/generated/
  filename: assets.dart

flutter_icons:
  android: "ic_launcher"
  remove_adaptive_icon: true
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/img/logo.png"
  ios: false
  image_path: "assets/img/logo.png"
#flutter pub run flutter_launcher_icons:main

flutter:
  uses-material-design: true



  assets:
    - assets/img/
    - assets/animations/
    - assets/helper/

