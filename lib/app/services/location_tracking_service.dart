import 'dart:async';
import 'dart:ui';
import 'package:app/app/data/models/order_model.dart';
import 'package:app/app/data/network/repositories/driver_order_track_repository.dart';
import 'package:flutter/material.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:geolocator/geolocator.dart';
import 'package:get_storage/get_storage.dart';

class LocationTrackingService {
  static const String _channelId = 'location_tracking_channel';
  static const String _channelName = 'Location Tracking';
  static const String _notificationTitle = 'Tracking Location';
  static const String _notificationBody = 'Your location is being tracked for delivery';

  static final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  static bool _isServiceRunning = false;
  static StreamSubscription<Position>? _locationSubscription;
  static OrderModel? _currentOrder;

  /// Initialize the background service
  static Future<void> initializeService() async {
    final service = FlutterBackgroundService();

    // Configure notification channel
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      _channelId,
      _channelName,
      description: 'Channel for location tracking notifications',
      importance: Importance.low,
      playSound: false,
      enableVibration: false,
    );

    await _notificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);

    // Configure the service
    await service.configure(
      iosConfiguration: IosConfiguration(
        autoStart: false,
        onForeground: onStart,
        onBackground: onIosBackground,
      ),
      androidConfiguration: AndroidConfiguration(
        onStart: onStart,
        autoStart: false,
        isForegroundMode: true,
        notificationChannelId: _channelId,
        initialNotificationTitle: _notificationTitle,
        initialNotificationContent: _notificationBody,
        foregroundServiceNotificationId: 888,
      ),
    );
  }

  /// Start location tracking service
  static Future<void> startTracking(OrderModel order) async {
    if (_isServiceRunning) {
      await stopTracking();
    }

    _currentOrder = order;

    // Store order data for background access
    final storage = GetStorage();
    await storage.write('tracking_order', order.toJson());

    final service = FlutterBackgroundService();

    // Check if service is already running
    bool isRunning = await service.isRunning();
    if (!isRunning) {
      await service.startService();
    }

    _isServiceRunning = true;

    // Send start tracking command
    service.invoke('start_tracking', {
      'order': order.toJson(),
    });
  }

  /// Stop location tracking service
  static Future<void> stopTracking() async {
    if (!_isServiceRunning) return;

    final service = FlutterBackgroundService();
    service.invoke('stop_tracking');

    _locationSubscription?.cancel();
    _locationSubscription = null;
    _currentOrder = null;
    _isServiceRunning = false;

    // Clear stored order data
    final storage = GetStorage();
    await storage.remove('tracking_order');
  }

  /// Check if service is currently running
  static bool get isRunning => _isServiceRunning;

  /// Get current tracking order
  static OrderModel? get currentOrder => _currentOrder;

  /// Background service entry point
  @pragma('vm:entry-point')
  static void onStart(ServiceInstance service) async {
    DartPluginRegistrant.ensureInitialized();

    // Initialize storage
    await GetStorage.init();

    service.on('start_tracking').listen((event) async {
      final orderData = event?['order'];
      if (orderData != null) {
        final order = OrderModel.fromJson(orderData);
        await _startLocationTracking(service, order);
      }
    });

    service.on('stop_tracking').listen((event) async {
      await _stopLocationTracking(service);
    });

    // Handle service stop
    service.on('stopService').listen((event) async {
      await _stopLocationTracking(service);
      service.stopSelf();
    });
  }

  /// iOS background handler
  @pragma('vm:entry-point')
  static Future<bool> onIosBackground(ServiceInstance service) async {
    WidgetsFlutterBinding.ensureInitialized();
    DartPluginRegistrant.ensureInitialized();
    return true;
  }

  /// Start location tracking in background
  static Future<void> _startLocationTracking(ServiceInstance service, OrderModel order) async {
    // Check location permission
    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied || permission == LocationPermission.deniedForever) {
      service.invoke('permission_denied');
      return;
    }

    // Configure location settings for background
    const LocationSettings locationSettings = LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 10, // Update every 10 meters
      timeLimit: Duration(seconds: 30), // Timeout for location requests
    );

    // Start location stream
    _locationSubscription = Geolocator.getPositionStream(
      locationSettings: locationSettings,
    ).listen(
      (Position position) async {
        await _handleLocationUpdate(service, position, order);
      },
      onError: (error) {
        service.invoke('location_error', {'error': error.toString()});
      },
    );

    // Update notification
    await _updateNotification(
      'Tracking active',
      'Location tracking is active for order #${order.id}',
    );
  }

  /// Stop location tracking in background
  static Future<void> _stopLocationTracking(ServiceInstance service) async {
    _locationSubscription?.cancel();
    _locationSubscription = null;

    await _updateNotification(
      'Tracking stopped',
      'Location tracking has been stopped',
    );
  }

  /// Handle location updates in background
  static Future<void> _handleLocationUpdate(
    ServiceInstance service,
    Position position,
    OrderModel order
  ) async {
    try {
      // Send location to server
      await networkDriverSendOrderTrack(position, order);

      // Update notification with current location info
      await _updateNotification(
        'Location Updated',
        'Lat: ${position.latitude.toStringAsFixed(6)}, Lng: ${position.longitude.toStringAsFixed(6)}',
      );

      // Notify UI about location update
      service.invoke('location_update', {
        'latitude': position.latitude,
        'longitude': position.longitude,
        'accuracy': position.accuracy,
        'timestamp': position.timestamp.millisecondsSinceEpoch,
      });

    } catch (error) {
      service.invoke('network_error', {'error': error.toString()});
    }
  }

  /// Update foreground notification
  static Future<void> _updateNotification(String title, String body) async {
    const AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      _channelId,
      _channelName,
      channelDescription: 'Channel for location tracking notifications',
      importance: Importance.low,
      priority: Priority.low,
      ongoing: true,
      autoCancel: false,
      playSound: false,
      enableVibration: false,
      icon: '@mipmap/ic_launcher',
    );

    const NotificationDetails notificationDetails = NotificationDetails(
      android: androidDetails,
    );

    await _notificationsPlugin.show(
      888, // Same ID as foreground service
      title,
      body,
      notificationDetails,
    );
  }
}
