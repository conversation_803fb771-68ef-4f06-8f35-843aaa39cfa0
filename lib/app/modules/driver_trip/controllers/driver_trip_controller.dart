import 'package:app/app/data/models/order_model.dart';
import 'package:app/app/data/network/repositories/driver_order_track_repository.dart';
import 'package:app/app/utils/helper/permission_helper.dart';
import 'package:app/app/utils/translations/strings.dart';
import 'package:app/app/utils/custom_snackbar.dart';
import 'package:app/app/services/location_tracking_service.dart';
import 'package:get/get.dart';
import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';
import 'package:http/http.dart' as http;
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:permission_handler/permission_handler.dart';
import 'dart:convert';
import 'dart:async';

class DriverTripController extends GetxController {
  // Order data
  late OrderModel order;

  // Location data
  final currentLocation = Rxn<Position>();
  final isLoadingLocation = false.obs;
  final hasLocationPermission = false.obs;
  final currentBearing = 0.0.obs; // For marker rotation

  // Location streaming
  StreamSubscription<Position>? _locationSubscription;
  final isLocationStreamActive = false.obs;

  // Background service
  final isBackgroundServiceActive = false.obs;
  final hasBackgroundLocationPermission = false.obs;

  // Trip details card
  final isDetailsExpanded = true.obs;

  // Map state
  final isMapReady = false.obs;
  final routeError = ''.obs;

  // Route data
  final routePoints = <LatLng>[].obs;
  final isLoadingRoute = false.obs;
  final distance = ''.obs;
  final duration = ''.obs;

  @override
  void onInit() {
    super.onInit();
    // Get order from arguments
    if (Get.arguments != null && Get.arguments is OrderModel) {
      order = Get.arguments as OrderModel;
    } else {
      // Handle error - no order provided
      Get.back();
      return;
    }

    _initializeTrip();
  }

  Future<void> _initializeTrip() async {
    await _checkLocationPermission();
    if (hasLocationPermission.value) {
      await _getCurrentLocation();
      await _checkBackgroundLocationPermission();
      await _startBackgroundLocationTracking();
      _startLocationStream();
    }
  }

  Future<void> _checkLocationPermission() async {
    try {
      bool permission = await PermissionHelper.location();
      hasLocationPermission.value = permission;
    } catch (e) {
      hasLocationPermission.value = false;
      AppSnackbar.show(
        message: Strings().locationPermissionDenied,
      );
    }
  }

  Future<void> _checkBackgroundLocationPermission() async {
    try {
      // Check for background location permission
      PermissionStatus status = await Permission.locationAlways.status;

      if (status.isGranted) {
        hasBackgroundLocationPermission.value = true;
      } else if (status.isDenied) {
        // Request background location permission
        PermissionStatus newStatus = await Permission.locationAlways.request();
        hasBackgroundLocationPermission.value = newStatus.isGranted;

        if (!newStatus.isGranted) {
          AppSnackbar.show(
            message: 'Background location permission is required for continuous tracking',
          );
        }
      } else {
        hasBackgroundLocationPermission.value = false;
        AppSnackbar.show(
          message: 'Background location permission is permanently denied',
        );
      }
    } catch (e) {
      hasBackgroundLocationPermission.value = false;
      AppSnackbar.show(
        message: 'Failed to check background location permission',
      );
    }
  }

  Future<void> _getCurrentLocation() async {
    if (!hasLocationPermission.value) return;

    isLoadingLocation.value = true;

    try {
      // Check if location service is enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        AppSnackbar.show(
          message: Strings().enableLocationServices,
        );
        isLoadingLocation.value = false;
        return;
      }

      Position position = await Geolocator.getCurrentPosition();

      currentLocation.value = position;
      isMapReady.value = true; // Mark as ready when location is obtained

      // Calculate route if destination is available
      if (order.endLatitude != null && order.endLongitude != null) {
        await _calculateRoute();
      }
    } catch (e) {
      AppSnackbar.show(
        message: Strings().failedToGetCurrentLocation,
      );
    }

    isLoadingLocation.value = false;
  }

  Future<void> _calculateRoute() async {
    if (currentLocation.value == null ||
        order.endLatitude == null ||
        order.endLongitude == null) {
      return;
    }

    isLoadingRoute.value = true;
    routeError.value = '';

    try {
      final startLat = currentLocation.value!.latitude;
      final startLng = currentLocation.value!.longitude;
      final endLat = double.parse(order.endLatitude!);
      final endLng = double.parse(order.endLongitude!);

      // Try to get route from OpenRouteService
      final routeData = await _getRouteFromOpenRouteService(
        startLat, startLng, endLat, endLng,
      );

      if (routeData != null) {
        routePoints.value = routeData['coordinates'];
        distance.value = routeData['distance'];
        duration.value = routeData['duration'];
      } else {
        // Fallback to straight line if no route found
        routePoints.value = [
          LatLng(startLat, startLng),
          LatLng(endLat, endLng),
        ];

        // Calculate straight line distance
        final distanceInMeters = Geolocator.distanceBetween(
          startLat, startLng, endLat, endLng,
        );
        distance.value = '${(distanceInMeters / 1000).toStringAsFixed(1)} km';
        duration.value = 'N/A';
      }
    } catch (e) {
      routeError.value = 'Failed to calculate route: $e';

      // Fallback to straight line
      final startLat = currentLocation.value!.latitude;
      final startLng = currentLocation.value!.longitude;
      final endLat = double.parse(order.endLatitude!);
      final endLng = double.parse(order.endLongitude!);

      routePoints.value = [
        LatLng(startLat, startLng),
        LatLng(endLat, endLng),
      ];

      final distanceInMeters = Geolocator.distanceBetween(
        startLat, startLng, endLat, endLng,
      );
      distance.value = '${(distanceInMeters / 1000).toStringAsFixed(1)} km';
      duration.value = 'N/A';
    }

    isLoadingRoute.value = false;
  }

  Future<Map<String, dynamic>?> _getRouteFromOpenRouteService(
    double startLat, double startLng, double endLat, double endLng,
  ) async {
    try {
      // Using OSRM demo server (free but limited)
      final url = 'http://router.project-osrm.org/route/v1/driving/'
          '$startLng,$startLat;$endLng,$endLat?overview=full&geometries=geojson';

      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['routes'] != null && data['routes'].isNotEmpty) {
          final route = data['routes'][0];
          final geometry = route['geometry'];
          final distance = route['distance'];
          final duration = route['duration'];

          // Convert coordinates to LatLng list
          final coordinates = <LatLng>[];
          if (geometry['coordinates'] != null) {
            for (final coord in geometry['coordinates']) {
              coordinates.add(LatLng(coord[1], coord[0])); // Note: OSRM returns [lng, lat]
            }
          }

          return {
            'coordinates': coordinates,
            'distance': '${(distance / 1000).toStringAsFixed(1)} km',
            'duration': '${(duration / 60).toStringAsFixed(0)} min',
          };
        }
      }
    } catch (e) {
      // Log error silently in production
      routeError.value = 'Route calculation failed';
    }

    return null;
  }

  void toggleTripDetails() {
    isDetailsExpanded.value = !isDetailsExpanded.value;
  }

  void refreshLocation() {
    if (hasLocationPermission.value) {
      _getCurrentLocation();
    } else {
      _checkLocationPermission();
    }
  }

  // Location streaming methods
  void _startLocationStream() {
    if (!hasLocationPermission.value) return;

    isLocationStreamActive.value = true;

    const LocationSettings locationSettings = LocationSettings(
      accuracy: LocationAccuracy.high,
      distanceFilter: 10, // Update every 10 meters
    );

    _locationSubscription = Geolocator.getPositionStream(
      locationSettings: locationSettings,
    ).listen(
      (Position position) {
        _updateLocationFromStream(position);
        networkDriverSendOrderTrack(position,order);
      },
      onError: (error) {
        AppSnackbar.show(
          message: 'Location stream error: $error',
        );
        isLocationStreamActive.value = false;
      },
    );
  }

  void _updateLocationFromStream(Position newPosition) {
    if (currentLocation.value != null) {
      // Calculate bearing between old and new position
      double bearing = Geolocator.bearingBetween(
        currentLocation.value!.latitude,
        currentLocation.value!.longitude,
        newPosition.latitude,
        newPosition.longitude,
      );

      // Only update bearing if there's significant movement
      double distance = Geolocator.distanceBetween(
        currentLocation.value!.latitude,
        currentLocation.value!.longitude,
        newPosition.latitude,
        newPosition.longitude,
      );

      if (distance > 5) { // 5 meters threshold
        currentBearing.value = bearing;
      }
    }

    currentLocation.value = newPosition;

    // Recalculate route if destination exists and location changed significantly
    if (order.endLatitude != null && order.endLongitude != null) {
      _calculateRoute();
    }
  }

  void _stopLocationStream() {
    _locationSubscription?.cancel();
    _locationSubscription = null;
    isLocationStreamActive.value = false;
  }

  // Get current location as LatLng for map
  LatLng? get currentLatLng {
    if (currentLocation.value != null) {
      return LatLng(
        currentLocation.value!.latitude,
        currentLocation.value!.longitude,
      );
    }
    return null;
  }

  // Get destination location as LatLng for map
  LatLng? get destinationLatLng {
    if (order.endLatitude != null && order.endLongitude != null) {
      try {
        return LatLng(
          double.parse(order.endLatitude!),
          double.parse(order.endLongitude!),
        );
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  String get orderStatusText {
    return order.orderStatusName ?? Strings().tripStart;
  }

  String get startAddressText {
    return order.startAddress ?? '-';
  }

  String get endAddressText {
    return order.endAddress ?? '-';
  }

  String get orderNumberText {
    return '${Strings().orderNumber}: ${order.orderNumber ?? order.id}';
  }

  // Background location tracking methods
  Future<void> _startBackgroundLocationTracking() async {
    if (!hasBackgroundLocationPermission.value) {
      AppSnackbar.show(
        message: 'Background location permission required for continuous tracking',
      );
      return;
    }

    try {
      await LocationTrackingService.startTracking(order);
      isBackgroundServiceActive.value = true;

      // Listen to service events
      final service = FlutterBackgroundService();
      service.on('location_update').listen((event) {
        if (event != null) {
          // Update UI with background location updates
          final lat = event['latitude'] as double?;
          final lng = event['longitude'] as double?;
          final accuracy = event['accuracy'] as double?;
          final timestamp = event['timestamp'] as int?;

          if (lat != null && lng != null) {
            final position = Position(
              latitude: lat,
              longitude: lng,
              timestamp: timestamp != null ? DateTime.fromMillisecondsSinceEpoch(timestamp) : DateTime.now(),
              accuracy: accuracy ?? 0.0,
              altitude: 0.0,
              altitudeAccuracy: 0.0,
              heading: 0.0,
              headingAccuracy: 0.0,
              speed: 0.0,
              speedAccuracy: 0.0,
            );

            _updateLocationFromStream(position);
          }
        }
      });

      service.on('permission_denied').listen((event) {
        AppSnackbar.show(
          message: 'Location permission denied in background service',
        );
        isBackgroundServiceActive.value = false;
      });

      service.on('location_error').listen((event) {
        AppSnackbar.show(
          message: 'Background location error: ${event?['error'] ?? 'Unknown error'}',
        );
      });

      service.on('network_error').listen((event) {
        AppSnackbar.show(
          message: 'Network error in background service: ${event?['error'] ?? 'Unknown error'}',
        );
      });

      AppSnackbar.show(
        message: 'Background location tracking started',
      );

    } catch (e) {
      AppSnackbar.show(
        message: 'Failed to start background tracking: $e',
      );
      isBackgroundServiceActive.value = false;
    }
  }

  Future<void> _stopBackgroundLocationTracking() async {
    try {
      await LocationTrackingService.stopTracking();
      isBackgroundServiceActive.value = false;

      AppSnackbar.show(
        message: 'Background location tracking stopped',
      );
    } catch (e) {
      AppSnackbar.show(
        message: 'Failed to stop background tracking: $e',
      );
    }
  }

  // Navigation methods
  Future<void> navigateToCurrentLocation() async {
    // This method will be used by the view to center map on current location
    // Implementation will be in the view layer
  }

  Future<void> navigateToDestination() async {
    if (order.endLatitude == null || order.endLongitude == null) {
      AppSnackbar.show(
        message: 'Destination not available',
      );
      return;
    }

    try {
      final lat = double.parse(order.endLatitude!);
      final lng = double.parse(order.endLongitude!);

      // Try Google Maps first
      String googleMapsUrl = 'https://www.google.com/maps/dir/?api=1&destination=$lat,$lng';
      Uri googleMapsUri = Uri.parse(googleMapsUrl);

      if (await canLaunchUrl(googleMapsUri)) {
        await launchUrl(googleMapsUri, mode: LaunchMode.externalApplication);
        return;
      }

      // Fallback to Apple Maps on iOS
      String appleMapsUrl = 'https://maps.apple.com/?daddr=$lat,$lng';
      Uri appleMapsUri = Uri.parse(appleMapsUrl);

      if (await canLaunchUrl(appleMapsUri)) {
        await launchUrl(appleMapsUri, mode: LaunchMode.externalApplication);
        return;
      }

      // Final fallback to generic maps URL
      String fallbackUrl = 'https://www.google.com/maps/search/?api=1&query=$lat,$lng';
      Uri fallbackUri = Uri.parse(fallbackUrl);

      if (await canLaunchUrl(fallbackUri)) {
        await launchUrl(fallbackUri, mode: LaunchMode.externalApplication);
      } else {
        AppSnackbar.show(
          message: 'No navigation app available',
        );
      }
    } catch (e) {
      AppSnackbar.show(
        message: 'Navigation error occurred',
      );
    }
  }

  @override
  void onClose() {
    _stopLocationStream();
    _stopBackgroundLocationTracking();
    super.onClose();
  }
}
