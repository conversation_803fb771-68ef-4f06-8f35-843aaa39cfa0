import 'package:app/app/utils/theme/app_colors.dart';
import 'package:app/app/utils/translations/strings.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import 'dart:math' as math;
import '../controllers/driver_trip_controller.dart';

class DriverTripView extends GetView<DriverTripController> {
  DriverTripView({super.key});

  final MapController _mapController = MapController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(Strings().tripDetails),
        centerTitle: true,
        backgroundColor: AppColor.clr(AppColor.primary),
        foregroundColor: Colors.white,
        actions: [
          Obx(() => IconButton(
                onPressed: controller.refreshLocation,
                icon: controller.isLoadingLocation.value
                    ? SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Icon(Icons.my_location),
              )),
        ],
      ),
      body: Stack(
        children: [
          // Map
          _buildMap(),

          // Navigation buttons
          _buildNavigationButtons(),

          // Trip Details Card
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: _buildTripDetailsCard(),
          ),

          // Location permission overlay
          Obx(() {
            if (!controller.hasLocationPermission.value) {
              return _buildLocationPermissionOverlay();
            }
            return SizedBox.shrink();
          }),
        ],
      ),
    );
  }

  Widget _buildMap() {
    return Obx(() {
      // Determine map center and zoom
      LatLng? center;
      double zoom = 10.0;

      if (controller.currentLatLng != null) {
        center = controller.currentLatLng!;
        zoom = 15.0;
      } else if (controller.destinationLatLng != null) {
        center = controller.destinationLatLng!;
        zoom = 15.0;
      } else {
        // Default to Baghdad coordinates if no location available
        center = LatLng(33.3152, 44.3661);
        zoom = 10.0;
      }

      return Stack(
        children: [
          // OpenStreetMap
          FlutterMap(
            mapController: _mapController,
            options: MapOptions(
              initialCenter: center,
              initialZoom: zoom,
              minZoom: 5.0,
              maxZoom: 18.0,
            ),
            children: [
              // OpenStreetMap tile layer
              TileLayer(
                urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                userAgentPackageName: 'com.example.app',
                maxZoom: 18,
              ),

              // Route polyline
              if (controller.routePoints.isNotEmpty)
                PolylineLayer(
                  polylines: [
                    Polyline(
                      points: controller.routePoints,
                      strokeWidth: 4.0,
                      color: AppColor.clr(AppColor.primary),
                    ),
                  ],
                ),

              // Markers layer
              MarkerLayer(
                markers: [
                  // Current location marker
                  if (controller.currentLatLng != null)
                    Marker(
                      point: controller.currentLatLng!,
                      width: 40,
                      height: 40,
                      child: Obx(() => Transform.rotate(
                            angle: controller.currentBearing.value * (math.pi / 180),
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.blue,
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(color: Colors.white, width: 3),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.3),
                                    blurRadius: 6,
                                    offset: Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Icon(
                                Icons.navigation,
                                color: Colors.white,
                                size: 20,
                              ),
                            ),
                          )),
                    ),

                  // Destination marker
                  if (controller.destinationLatLng != null)
                    Marker(
                      point: controller.destinationLatLng!,
                      width: 40,
                      height: 40,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 3),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.3),
                              blurRadius: 6,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.location_on,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),

          // Loading overlays
          if (controller.isLoadingLocation.value)
            Container(
              color: Colors.black.withOpacity(0.3),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        AppColor.clr(AppColor.primary),
                      ),
                    ),
                    SizedBox(height: 16),
                    Text(
                      Strings().gettingCurrentLocation,
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
            ),

          if (controller.isLoadingRoute.value)
            Positioned(
              top: 16,
              left: 16,
              right: 16,
              child: Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          AppColor.clr(AppColor.primary),
                        ),
                      ),
                    ),
                    SizedBox(width: 12),
                    Text(
                      'Calculating route...',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),

          // Route info overlay
          if (controller.distance.value.isNotEmpty && !controller.isLoadingRoute.value)
            Positioned(
              top: 16,
              left: 16,
              right: 16,
              child: Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.route,
                      color: AppColor.clr(AppColor.primary),
                      size: 20,
                    ),
                    SizedBox(width: 8),
                    Text(
                      controller.distance.value,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (controller.duration.value.isNotEmpty && controller.duration.value != 'N/A')
                      Text(
                        ' • ${controller.duration.value}',
                        style: TextStyle(fontSize: 14),
                      ),
                  ],
                ),
              ),
            ),

          // Background service status overlay
          Positioned(
            top: controller.distance.value.isNotEmpty && !controller.isLoadingRoute.value ? 80 : 16,
            left: 16,
            child: Obx(() => Container(
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: controller.isBackgroundServiceActive.value ? Colors.green : Colors.orange,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    controller.isBackgroundServiceActive.value ? Icons.gps_fixed : Icons.gps_not_fixed,
                    color: Colors.white,
                    size: 16,
                  ),
                  SizedBox(width: 6),
                  Text(
                    controller.isBackgroundServiceActive.value ? 'Background Tracking' : 'Foreground Only',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            )),
          ),
        ],
      );
    });
  }

  Widget _buildTripDetailsCard() {
    return Obx(() {
      return AnimatedContainer(
        duration: Duration(milliseconds: 300),
        height: controller.isDetailsExpanded.value ? 280 : 80,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              offset: Offset(0, -2),
            ),
          ],
        ),
        child: Column(
          children: [
            // Header with toggle button
            GestureDetector(
              onTap: controller.toggleTripDetails,
              child: Container(
                padding: EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(
                      Icons.local_shipping,
                      color: AppColor.clr(AppColor.primary),
                      size: 24,
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            controller.orderNumberText,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            controller.orderStatusText,
                            style: TextStyle(
                              fontSize: 14,
                              color: AppColor.clr(AppColor.primary),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Icon(
                      controller.isDetailsExpanded.value ? Icons.keyboard_arrow_down : Icons.keyboard_arrow_up,
                      color: Colors.grey[600],
                    ),
                  ],
                ),
              ),
            ),

            // Expanded details
            if (controller.isDetailsExpanded.value)
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.symmetric(horizontal: 16),
                  child: Column(
                    children: [
                      Divider(height: 1),
                      SizedBox(height: 16),

                      // Route information
                      _buildRouteInfo(),
                      SizedBox(height: 16),

                      // Order details
                      _buildOrderDetails(),
                      SizedBox(height: 16),
                    ],
                  ),
                ),
              ),
          ],
        ),
      );
    });
  }

  Widget _buildRouteInfo() {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          // Start location
          Row(
            children: [
              Icon(Icons.location_on, color: Colors.green, size: 20),
              SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      Strings().startLocation,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      controller.startAddressText,
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 12),

          // Destination
          Row(
            children: [
              Icon(Icons.location_on, color: Colors.red, size: 20),
              SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      Strings().destination,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      controller.endAddressText,
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildOrderDetails() {
    return Column(
      children: [
        if (controller.order.weight != null)
          _buildDetailRow(
            Icons.scale,
            Strings().weight,
            '${controller.order.weight} kg',
          ),
        if (controller.order.truckTypeName != null)
          _buildDetailRow(
            Icons.local_shipping,
            Strings().truckType,
            controller.order.truckTypeName!,
          ),
        if (controller.order.companyName != null)
          _buildDetailRow(
            Icons.business,
            Strings().companyNameField,
            controller.order.companyName!,
          ),
        if (controller.order.price != null)
          _buildDetailRow(
            Icons.attach_money,
            Strings().price,
            '${controller.order.price} IQD',
          ),
      ],
    );
  }

  Widget _buildDetailRow(IconData icon, String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 6),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          SizedBox(width: 8),
          Text(
            '$label: ',
            style: TextStyle(
              fontSize: 14,
              color: Colors.black87,
              fontWeight: FontWeight.w500,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(fontSize: 14, color: Colors.black87),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationPermissionOverlay() {
    return Container(
      color: Colors.black.withOpacity(0.7),
      child: Center(
        child: Container(
          margin: EdgeInsets.all(32),
          padding: EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.location_off,
                size: 64,
                color: Colors.red,
              ),
              SizedBox(height: 16),
              Text(
                Strings().locationPermissionRequired,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 8),
              Text(
                Strings().enableLocationServices,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 24),
              ElevatedButton(
                onPressed: controller.refreshLocation,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColor.clr(AppColor.primary),
                  foregroundColor: Colors.white,
                ),
                child: Text(Strings().enableLocationServices),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavigationButtons() {
    return Positioned(
      right: 16,
      bottom: 320, // Above the trip details card
      child: Column(
        children: [
          // Navigate to current location button
          Obx(() {
            double va = controller.currentBearing.value;
            return FloatingActionButton(
              heroTag: "current_location",
              onPressed: controller.currentLatLng != null ? () => _centerMapOnCurrentLocation() : null,
              backgroundColor: controller.currentLatLng != null ? AppColor.clr(AppColor.primary) : Colors.grey,
              mini: true,
              child: Icon(
                Icons.my_location,
                color: Colors.white,
              ),
            );
          }),
          SizedBox(height: 12),

          // Navigate to destination button
          Obx(() {
            double va = controller.currentBearing.value;
            return FloatingActionButton(
              heroTag: "navigate_destination",
              onPressed: controller.destinationLatLng != null ? () => controller.navigateToDestination() : null,
              backgroundColor: controller.destinationLatLng != null ? Colors.green : Colors.grey,
              mini: true,
              child: Icon(
                Icons.directions,
                color: Colors.white,
              ),
            );
          }),
        ],
      ),
    );
  }

  void _centerMapOnCurrentLocation() {
    if (controller.currentLatLng != null) {
      _mapController.move(controller.currentLatLng!, 15.0);
    }
  }
}
